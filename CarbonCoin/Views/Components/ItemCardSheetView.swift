//
//  ItemCardSheetView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/31.
//

import SwiftUI

// MARK: - 卡片Sheet视图
struct ItemCardSheetView: View {
    // MARK: - Properties
    @EnvironmentObject private var cardStore: CardStore
    @State private var sheetHeight: CGFloat = UIScreen.main.bounds.height / 3 // 初始高度为屏幕1/3
    @State private var isDragging = false
    @State private var draggedCard: ItemCard?

    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    private let fullHeight: CGFloat
    private let compactHeight: CGFloat

    // 拖拽处理回调
    let onCardDropped: (String, String) -> Void // (cardId, userId)

    init(onCardDropped: @escaping (String, String) -> Void) {
        self.onCardDropped = onCardDropped
        self.fullHeight = UIScreen.main.bounds.height / 3
        self.compactHeight = UIScreen.main.bounds.height / 6
    }

    var body: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            dragIndicator

            // 标题栏
            headerView

            // 卡片滚动视图
            cardScrollView

            Spacer()
        }
        .frame(height: sheetHeight)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground.opacity(0.95))
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .stroke(Color.cardBorderGradient, lineWidth: 1)
                )
        )
        .animation(Theme.AnimationStyle.normal, value: sheetHeight)
        .onAppear {
            // 加载用户卡片数据
            Task {
                await cardStore.loadUserCards()
            }
        }
    }

    // MARK: - 拖拽指示器
    private var dragIndicator: some View {
        RoundedRectangle(cornerRadius: 2)
            .fill(Color.textSecondary.opacity(0.5))
            .frame(width: 40, height: 4)
            .padding(.top, Theme.Spacing.sm)
    }

    // MARK: - 标题栏
    private var headerView: some View {
        HStack {
            Text("我的卡片")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Spacer()

            Text("\(cardStore.cards.count) 张")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.vertical, Theme.Spacing.sm)
    }

    // MARK: - 卡片滚动视图
    private var cardScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Theme.Spacing.md) {
                ForEach(cardStore.cards) { card in
                    DraggableCardThumbnail(
                        card: card,
                        onDragChanged: handleDragChanged,
                        onDragEnded: handleDragEnded
                    )
                }
            }
            .padding(.horizontal, Theme.Spacing.md)
        }
        .frame(height: 160)
    }

    // MARK: - 拖拽处理方法

    /// 处理拖拽状态变化
    private func handleDragChanged(_ card: ItemCard, _ value: DragGesture.Value) {
        draggedCard = card
        isDragging = true

        // 检查是否拖拽出sheet边界
        let dragY = value.translation.height
        if dragY < -50 { // 向上拖拽超过50点
            // 缩小sheet高度
            withAnimation(Theme.AnimationStyle.normal) {
                sheetHeight = compactHeight
            }
        } else if dragY > -20 && sheetHeight == compactHeight {
            // 拖拽回sheet区域，恢复高度
            withAnimation(Theme.AnimationStyle.normal) {
                sheetHeight = fullHeight
            }
        }
    }

    /// 处理拖拽结束
    private func handleDragEnded(_ card: ItemCard, _ value: DragGesture.Value) {
        isDragging = false
        draggedCard = nil

        // 恢复sheet高度
        withAnimation(Theme.AnimationStyle.normal) {
            sheetHeight = fullHeight
        }
    }
}

// MARK: - 可拖拽卡片缩略图
struct DraggableCardThumbnail: View {
    let card: ItemCard
    let onDragChanged: (ItemCard, DragGesture.Value) -> Void
    let onDragEnded: (ItemCard, DragGesture.Value) -> Void

    @State private var dragOffset = CGSize.zero
    @State private var isPressed = false

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
            // 卡片图片
            cardImage

            // 卡片标题
            cardTitle

            // 卡片标签
            cardTags
        }
        .frame(width: 120)
        .padding(Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.cardBackground.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                        .stroke(Color.brandGreen.opacity(isPressed ? 0.8 : 0.3), lineWidth: 1)
                )
        )
        .scaleEffect(isPressed ? 1.05 : 1.0)
        .offset(dragOffset)
        .animation(Theme.AnimationStyle.bouncy, value: isPressed)
        .animation(Theme.AnimationStyle.fast, value: dragOffset)
        .onTapGesture {
            // 单击跳转到详情页
            // TODO: 实现导航到详情页
            print("🔍 点击卡片: \(card.title)")
        }
        .onLongPressGesture(minimumDuration: 0.5) {
            // 长按开始拖拽
            isPressed = true
        }
        .simultaneousGesture(
            DragGesture()
                .onChanged { value in
                    dragOffset = value.translation
                    onDragChanged(card, value)
                }
                .onEnded { value in
                    dragOffset = .zero
                    isPressed = false
                    onDragEnded(card, value)
                }
        )
    }

    // MARK: - 卡片图片
    private var cardImage: some View {
        Group {
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(height: 80)
                    .clipped()
                    .cornerRadius(Theme.CornerRadius.sm)
            } else {
                Rectangle()
                    .fill(Color.cardBackground.opacity(0.3))
                    .frame(height: 80)
                    .cornerRadius(Theme.CornerRadius.sm)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title3)
                            .foregroundColor(.textSecondary)
                    )
            }
        }
    }

    // MARK: - 卡片标题
    private var cardTitle: some View {
        Text(card.title)
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(.textPrimary)
            .lineLimit(2)
            .multilineTextAlignment(.leading)
    }

    // MARK: - 卡片标签
    private var cardTags: some View {
        HStack {
            ForEach(Array(card.tags.prefix(1)), id: \.self) { tag in
                Text(tag)
                    .font(.caption2)
                    .foregroundColor(.textPrimary)
                    .padding(.horizontal, 4)
                    .padding(.vertical, 2)
                    .background(Color.brandGreen.opacity(0.2))
                    .cornerRadius(Theme.CornerRadius.sm)
            }

            if card.tags.count > 1 {
                Text("+\(card.tags.count - 1)")
                    .font(.caption2)
                    .foregroundColor(.textSecondary)
            }

            Spacer()
        }
    }
}

// MARK: - 预览
#Preview("卡片Sheet视图") {
    let sampleCards = [
        ItemCard(
            id: "card1",
            tags: ["塑料瓶", "可回收"],
            description: "这是一个塑料瓶",
            title: "可乐瓶",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "user1",
            location: "北京市",
            latitude: 39.9042,
            longitude: 116.4074
        ),
        ItemCard(
            id: "card2",
            tags: ["纸质", "包装"],
            description: "这是一个纸盒",
            title: "快递盒",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "user1",
            location: "上海市",
            latitude: 31.2304,
            longitude: 121.4737
        )
    ]

    let cardStore = CardStore()
    // 在预览中设置示例数据
    cardStore.cards = sampleCards

    return ItemCardSheetView { cardId, userId in
        print("卡片 \(cardId) 拖拽到用户 \(userId)")
    }
    .environmentObject(cardStore)
    .background(Color.black)
}
