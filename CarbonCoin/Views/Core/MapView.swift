//
//  MapView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import SwiftUI
import MapKit
import CoreLocation

/// 为系统类型 CLLocationCoordinate2D 添加 Equatable 扩展
extension CLLocationCoordinate2D: @retroactive Equatable {
    public static func == (lhs: CLLocationCoordinate2D, rhs: CLLocationCoordinate2D) -> Bool {
        lhs.latitude == rhs.latitude && lhs.longitude == rhs.longitude
    }
}

// MARK: - 地图视图组件
struct MapView: View {
    @StateObject private var viewModel: MapViewModel
    @StateObject private var friendMapViewModel: FriendMapViewModel
    @EnvironmentObject private var appSettings: AppSettings
    @Environment(\.dismiss) private var dismiss
    @State private var showLocationPermissionAlert = false

    @State private var isAnimating = false
    @State private var refreshTask: Task<Void, Never>?
    @State private var showCardSheet = false

    init() {
        self._viewModel = StateObject(wrappedValue: MapViewModel())
        self._friendMapViewModel = StateObject(wrappedValue: FriendMapViewModel())
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // MARK: 地图主体
                Map(position: $viewModel.cameraPosition) {
                    // 用户当前位置标记
                    if let userLocation = viewModel.userLocation {
                        Annotation("我在这里", coordinate: userLocation, anchor: .center) {
                            ZStack {
                                // 外圈脉冲效果
                                Circle()
                                    .fill(Color.brandGreen.opacity(0.3))
                                    .frame(width: 40, height: 40)
                                    .scaleEffect(1.5)
                                    .opacity(0.6)
                                    .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: userLocation)
                                // TODO: loop的动画：伴随scale和easeOut效果

                                // 内圈位置标记
                                if let userHeading = viewModel.userHeading{
                                    Image(systemName: "location.fill")
                                        .foregroundColor(.white)
                                        .font(.title2)
                                        .padding(8)
                                        .rotationEffect(Angle(degrees: userHeading))
                                        .background(
                                            Circle()
                                                .fill(Color.brandGreen)
                                                .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                                        )
                                }
                            }
                        }
                    }

                    // 好友位置标注
                    ForEach(friendMapViewModel.friendAnnotations) { annotation in
                        Annotation(
                            annotation.title,
                            coordinate: annotation.coordinate,
                            anchor: .center
                        ) {
                            FriendOnMapView(
                                friendLocation: annotation.friendLocation,
                                avatarSize: 45,
                                showDetails: true
                            )
                            .onTapGesture {
                                // 点击好友标注时的交互逻辑
                                print("🗺️ 点击了好友: \(annotation.friendLocation.nickname)")
                            }
                        }
                    }
                }
                .mapStyle(viewModel.mapStyle)
                .mapControls {
                    MapUserLocationButton()
                    MapCompass()
                    MapScaleView()
                }
                .ignoresSafeArea(.all)

                // MARK: 顶部导航栏
                VStack {
                    HStack {
                        // 关闭按钮
                        Button(action: {
                            dismiss()
                        }) {
                            Image(systemName: "xmark")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding(12)
                                .background(
                                    Circle()
                                        .fill(.ultraThinMaterial)
                                )
                        }
                        .padding(.leading, Theme.Spacing.md)

                        Spacer()

                        // 标题
                        Text("碳迹地图")
                            .font(.title2Brand)
                            .foregroundColor(.white)
                            .padding(.horizontal, Theme.Spacing.lg)
                            .padding(.vertical, Theme.Spacing.sm)
                            .background(
                                Capsule()
                                    .fill(.ultraThinMaterial)
                            )

                        Spacer()

                        // 功能按钮组
                        HStack(spacing: Theme.Spacing.sm) {
                            // 好友位置刷新按钮
                            Button(action: {
                                Task {
                                    await friendMapViewModel.refreshFriendLocations(for: appSettings.userId)
                                }
                            }) {
                                Image(systemName: friendMapViewModel.isLoadingFriendLocations ? "arrow.clockwise" : "person.2.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)
                                    .padding(12)
                                    .background(
                                        Circle()
                                            .fill(.ultraThinMaterial)
                                    )
                                    .opacity( friendMapViewModel.isLoadingFriendLocations ? 0.5 : 1)
                            }
                            .disabled(friendMapViewModel.isLoadingFriendLocations)

                            // 地图样式切换按钮
                            Button(action: {
                                viewModel.toggleMapStyle()
                            }) {
                                Image(systemName: "map")
                                    .font(.title2)
                                    .foregroundColor(.white)
                                    .padding(12)
                                    .background(
                                        Circle()
                                            .fill(.ultraThinMaterial)
                                    )
                            }
                        }
                        .padding(.trailing, Theme.Spacing.md)
                    }
                    .padding(.top, Theme.Spacing.md)

                    Spacer()
                }

                // MARK: 底部功能
                BottomControlsView(
                    viewModel: viewModel,
                    friendMapViewModel: friendMapViewModel
                )
            }
            .toolbar {
                ToolbarItemGroup(placement: .bottomBar) {
                    // 卡片按钮
                    Button {
                        showCardSheet = true
                    } label: {
                        HStack {
                            Image(systemName: "rectangle.stack")
                                .font(.title3)
                            Text("卡片")
                                .font(.captionBrand)
                        }
                        .foregroundColor(.brandGreen)
                    }

                    // 表情按钮
                    Button {
                        // TODO: 实现表情功能
                    } label: {
                        HStack {
                            Image(systemName: "face.smiling")
                                .font(.title3)
                            Text("表情")
                                .font(.captionBrand)
                        }
                        .foregroundColor(.textSecondary)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .alert("位置权限", isPresented: $showLocationPermissionAlert) {
            Button("去设置") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage ?? "需要位置权限才能显示您的当前位置")
        }
        .alert("好友位置", isPresented: .constant(friendMapViewModel.errorMessage != nil)) {
            Button("确定") {
                friendMapViewModel.clearMessages()
            }
        } message: {
            if let errorMessage = friendMapViewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .onAppear {
            viewModel.startLocationUpdates()
            isAnimating = true
            
            // 立即获取好友位置信息
            Task{
                await friendMapViewModel.loadFriendLocations(for: appSettings.userId)
            }
            
            // 定时更新好友位置信息
            refreshTask = Task {
                while !Task.isCancelled {
                    try? await Task.sleep(for: .seconds(60))
                    await friendMapViewModel.refreshFriendLocations(for: appSettings.userId)
                    print("定时自动更新好友位置信息")
                }
            }
        }
        .onDisappear {
            viewModel.stopLocationUpdates()
            refreshTask?.cancel()
            refreshTask = nil
        }
        .onChange(of: viewModel.errorMessage) { _, newValue in
            if newValue != nil {
                showLocationPermissionAlert = true
            }
        }
        .sheet(isPresented: $showCardSheet) {
            ItemCardSheetView { cardId, userId in
                handleCardDropToFriend(cardId: cardId, userId: userId)
            }
            .environmentObject(CardStore())
            .presentationDetents([.fraction(1/3), .fraction(1/6)])
            .presentationDragIndicator(.visible)
            .presentationBackgroundInteraction(.enabled)
        }
    }

    // MARK: - 卡片拖放处理函数

    /// 处理卡片拖放到好友位置
    private func handleCardDropToFriend(cardId: String, userId: String) {
        print("🎯 卡片 \(cardId) 被拖放到好友 \(userId)")
        // TODO: 实现具体的卡片传输逻辑
        // 这里可以调用相应的API来处理卡片传输

        // 显示成功提示
        if let friend = friendMapViewModel.friendAnnotations.first(where: { $0.friendLocation.userId == userId }) {
            print("✅ 卡片已发送给 \(friend.friendLocation.nickname)")
        }

        // 关闭sheet
        showCardSheet = false
    }
}

// MARK: - 底部控件视图
private struct BottomControlsView: View {
    let viewModel: MapViewModel
    let friendMapViewModel: FriendMapViewModel

    var body: some View {
        VStack {
            Spacer()

            HStack {
                // 好友位置状态显示
                if friendMapViewModel.hasFriendLocations {
                    FriendLocationStatsView(friendMapViewModel: friendMapViewModel)
                        .padding(.leading, Theme.Spacing.md)
                }

                Spacer()

                MapControlButtonsView(viewModel: viewModel)
                    .padding(.trailing, Theme.Spacing.md)
            }
            .padding(.bottom, Theme.Spacing.xl)
        }
    }
}

// MARK: - 好友位置统计视图
private struct FriendLocationStatsView: View {
    let friendMapViewModel: FriendMapViewModel

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            let stats = friendMapViewModel.friendLocationStats
            HStack(spacing: 8) {
                Image(systemName: "person.2.fill")
                    .foregroundColor(.brandGreen)
                    .font(.caption)

                Text("好友: \(stats.total)")
                    .font(.caption)
                    .foregroundColor(.white)

                Circle()
                    .fill(Color.green)
                    .frame(width: 6, height: 6)

                Text("\(stats.online)")
                    .font(.caption)
                    .foregroundColor(.green)

                Circle()
                    .fill(Color.gray)
                    .frame(width: 6, height: 6)

                Text("\(stats.offline)")
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            if let updateTime = friendMapViewModel.formattedLastUpdateTime {
                Text(updateTime)
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

// MARK: - 地图控制按钮视图
private struct MapControlButtonsView: View {
    let viewModel: MapViewModel

    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 定位按钮
            Button(action: {
                viewModel.moveToUserLocation()
            }) {
                Image(systemName: "location.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding(12)
                    .background(
                        Circle()
                            .fill(Color.brandGreen)
                            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                    )
            }

            // 缩放按钮组
            VStack(spacing: 4) {
                Button(action: {
                    viewModel.zoomToLevel(viewModel.mapRegion.span.latitudeDelta * 0.5)
                }) {
                    Image(systemName: "plus")
                        .font(.title3)
                        .foregroundColor(.white)
                        .padding(8)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                        )
                }

                Button(action: {
                    viewModel.zoomToLevel(viewModel.mapRegion.span.latitudeDelta * 2.0)
                }) {
                    Image(systemName: "minus")
                        .font(.title3)
                        .foregroundColor(.white)
                        .padding(8)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                        )
                }
            }
        }
    }
}

#Preview {
    @State var appsettings = AppSettings()
    MapView()
        .stableBackground()
        .environmentObject(appsettings)
}
